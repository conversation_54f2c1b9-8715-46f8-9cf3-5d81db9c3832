#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
验证复权因子路径修复

演示修复后的复权因子存储路径
"""

import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

def main():
    """主函数"""
    print("=== 复权因子路径修复验证 ===")
    
    try:
        from config.settings import DATA_ROOT
        from utils.data_processor.adjustment.dividend_factor_storage import DividendFactorStorage
        
        print(f"📁 DATA_ROOT配置: {DATA_ROOT}")
        print()
        
        # 创建存储管理器
        storage = DividendFactorStorage()
        print(f"📂 存储管理器数据根目录: {storage.data_root}")
        print()
        
        # 测试股票代码
        test_symbols = ['000001.SZ', '600000.SH']
        
        print("=== 复权因子存储路径测试 ===")
        for symbol in test_symbols:
            try:
                path = storage.get_storage_path(symbol)
                print(f"股票代码: {symbol}")
                print(f"存储路径: {path}")
                
                # 验证路径格式
                path_str = str(path)
                is_in_data_root = path_str.startswith(DATA_ROOT)
                is_correct_format = '/raw/' in path_str and 'dividend_factors.parquet' in path_str
                
                print(f"✅ 在DATA_ROOT下: {is_in_data_root}")
                print(f"✅ 格式正确: {is_correct_format}")
                
                if is_in_data_root and is_correct_format:
                    print("🎉 路径修复成功！")
                else:
                    print("❌ 路径修复失败！")
                print()
                
            except Exception as e:
                print(f"❌ 获取路径失败: {e}")
                print()
        
        print("=== 修复前后对比 ===")
        print("修复前路径格式: 项目目录/data/dividend_factors/{code}_{market}_dividend_factors.parquet")
        print("修复后路径格式: D:\\data\\raw\\{exchange}\\{code}\\dividend_factors.parquet")
        print()
        print("✅ 修复后复权因子将保存到配置的DATA_ROOT路径，而不是项目目录中")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
    except Exception as e:
        print(f"❌ 执行失败: {e}")


if __name__ == "__main__":
    main()
